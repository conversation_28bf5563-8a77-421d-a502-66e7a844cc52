<div class="overflow-y-auto no-scrollbar py-2" style="max-height: 75%">
    <ul class="space-y-2 pt-2">
        <li>
            <div hx-get="/" hx-target="#pageContentMain" hx-replace-url="true"
                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);" class="flex items-center topLevelNavigation gap-3 px-2 p-1 text-sm cursor-pointer font-medium text-gray-900 rounded-lg transition-all text-sm dark:text-white
                 hover:bg-gray-200 dark:hover:bg-gray-700 group">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide mt-0.5 lucide-house">
                    <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                    <path
                        d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                </svg>
                <span class="m-0 p-0">Domov</span>
            </div>
        </li>
        <li>
            <button aria-controls="dropdown-pages"
                class="flex gap-3 items-center topLevelNavigation transition-all p-1 px-2 w-full text-sm font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700"
                data-collapse-toggle="dropdown-pages" type="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-list">
                    <path d="M3 12h.01" />
                    <path d="M3 18h.01" />
                    <path d="M3 6h.01" />
                    <path d="M8 12h13" />
                    <path d="M8 18h13" />
                    <path d="M8 6h13" />
                </svg>
                <span class="flex-1 text-left text-sm whitespace-nowrap">Základné údaje</span>
                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.3"
                        d="m8 10 4 4 4-4" />
                </svg>

            </button>
            <ul class="hidden py-2 space-y-1" id="dropdown-pages">
                <li>
                    <div hx-get="/admin" hx-target="#pageContentMain"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        hx-replace-url="true" class="sub flex items-center text-sm font-medium text-gray-900 dark:text-gray-100 dark:hover:bg-gray-600 rounded-lg
                     transition duration-75 group 
                         hover:bg-gray-200 cursor-pointer">
                        Správca
                    </div>
                </li>
                <li>
                    <div hx-get="/klienti" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium dark:text-gray-100 dark:hover:bg-gray-600 text-gray-900 rounded-lg cursor-pointer transition duration-75 group hover:bg-gray-200">
                        Klienti
                    </div>
                </li>
                <li class="flex flex-wrap">
                    <button aria-controls="dropdown-pages"
                        class="sub flex items-center text-sm w-full font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700"
                        data-collapse-toggle="aktiva-pages" type="button">
                        <svg aria-hidden="true" class="w-4 h-4 mr-2 text-gray-500 dark:text-white" fill="currentColor"
                            viewBox="0 0 19 20" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M18.972.863a.913.913 0 0 0-.041-.207.956.956 0 0 0-.107-.19 1.01 1.01 0 0 0-.065-.116c-.008-.01-.02-.013-.028-.022a1.008 1.008 0 0 0-.174-.137 1.085 1.085 0 0 0-.141-.095 1.051 1.051 0 0 0-.171-.047.985.985 0 0 0-.207-.041C18.025.007 18.014 0 18 0h-3.207a1 1 0 1 0 0 2h.5l-4.552 3.9-3.5-.874a1 1 0 0 0-.867.189l-5 4a1 1 0 0 0 1.25 1.562L7.238 7.09l3.52.88a1 1 0 0 0 .892-.211L17 3.173v1.034a1 1 0 0 0 2 0V1a.9.9 0 0 0-.028-.137ZM13.5 9a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11Zm.24 4.591a3.112 3.112 0 0 1 1.935 1.374 2.036 2.036 0 0 1 .234 1.584 2.255 2.255 0 0 1-1.374 1.469.982.982 0 0 1-1.953.09 2.943 2.943 0 0 1-1.475-.92 1 1 0 0 1 1.536-1.283.953.953 0 0 0 .507.29.778.778 0 0 0 .831-.18 1.108 1.108 0 0 0-.714-.481 3.105 3.105 0 0 1-1.934-1.374 2.042 2.042 0 0 1-.233-1.584 2.264 2.264 0 0 1 1.45-1.493v-.03a1 1 0 0 1 2 0c.517.159.98.457 1.337.862a1.002 1.002 0 1 1-1.524 1.3.962.962 0 0 0-.507-.286.775.775 0 0 0-.829.18 1.113 1.113 0 0 0 .713.482ZM6 20a1 1 0 0 1-1-1v-6a1 1 0 1 1 2 0v6a1 1 0 0 1-1 1Zm-4 0a1 1 0 0 1-1-1v-4a1 1 0 1 1 2 0v4a1 1 0 0 1-1 1Z" />
                        </svg>
                        <span class="flex-1 text-left whitespace-nowrap">Aktíva</span>
                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                stroke-width="1.3" d="m8 10 4 4 4-4" />
                        </svg>
                    </button>
                    <ul class="hidden py-2 space-y-2" id="aktiva-pages">
                        <li>
                            <div hx-get="/aktiva/cenne-papiere" hx-target="#pageContentMain" hx-replace-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="sub2 flex items-center text-sm dark:text-gray-100 dark:hover:bg-gray-600 font-medium cursor-pointer text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                                Cenné papiere
                            </div>
                        </li>
                        <li>
                            <div hx-get="/aktiva/sankcny-zoznam" hx-target="#pageContentMain" hx-replace-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="sub2 flex items-center dark:text-gray-100 dark:hover:bg-gray-600 text-sm gap-2 font-medium cursor-pointer text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                    fill="none" stroke="red" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="lucide lucide-book-x">
                                    <path d="m14.5 7-5 5" />
                                    <path
                                        d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20" />
                                    <path d="m9.5 7 5 5" />
                                </svg> Sanknčný zoznam
                            </div>
                        </li>
                    </ul>
                </li>
                <li>
                    <div hx-get="/menove-pary" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium dark:text-gray-100 dark:hover:bg-gray-600 text-gray-900 rounded-lg cursor-pointer transition duration-75 group hover:bg-gray-200">
                        Menový pár</div>
                </li>
            </ul>
        </li>
        <li>
            <button aria-controls="dropdown-sales"
                class="flex gap-4 items-center topLevelNavigation transition-all p-1 px-2 w-full text-sm font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700"
                data-collapse-toggle="dropdown-sales" type="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-list-checks">
                    <path d="m3 17 2 2 4-4" />
                    <path d="m3 7 2 2 4-4" />
                    <path d="M13 6h8" />
                    <path d="M13 12h8" />
                    <path d="M13 18h8" />
                </svg>
                <span class="flex-1 text-left whitespace-nowrap">Prehľady</span>
                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.3"
                        d="m8 10 4 4 4-4" />
                </svg>
            </button>
            <ul class="hidden py-2 space-y-2" id="dropdown-sales">
                <li>
                    <div hx-get="/prehlady/klientsky-vypis" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium cursor-pointer dark:text-gray-100 dark:hover:bg-gray-600 text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                        Klientsky výpis
                    </div>
                </li>
                <li>
                    <div hx-get="/prehlady/vypis-o-stave-majetku" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium cursor-pointer dark:text-gray-100 dark:hover:bg-gray-600 text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                        Výpis o stave majetku
                    </div>
                </li>
                <li>
                    <div hx-get="/prehlady/transakcie" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium cursor-pointer dark:text-gray-100 dark:hover:bg-gray-600 text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                        Prehľad transakcii
                    </div>
                </li>
                <li class="flex flex-wrap">
                    <button aria-controls="dropdown-pages"
                        class="sub flex gap-2 items-center text-sm w-full font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700"
                        data-collapse-toggle="ucty-pages" type="button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-wallet-cards">
                            <rect width="18" height="18" x="3" y="3" rx="2" />
                            <path d="M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2" />
                            <path
                                d="M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21" />
                        </svg>
                        <span class="flex-1 text-left whitespace-nowrap">Účty</span>
                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                stroke-width="1.3" d="m8 10 4 4 4-4" />
                        </svg>
                    </button>
                    <ul class="hidden py-2 space-y-2" id="ucty-pages">
                        <li>
                            <div hx-get="/prehlady/ucty/bezny-ucet" hx-target="#pageContentMain" hx-replace-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="sub2 flex items-center text-sm font-medium dark:text-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                                Bežný účet
                            </div>
                        </li>
                        <li>
                            <div hx-get="/prehlady/ucty/terminovany-ucet" hx-target="#pageContentMain"
                                hx-replace-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="sub2 flex items-center text-sm font-medium dark:text-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                                Terminovaný účet
                            </div>
                        </li>
                        <li>
                            <div hx-get="/prehlady/ucty/majetkovy-ucet" hx-target="#pageContentMain"
                                hx-replace-url="true"
                                onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                                class="sub2 flex items-center text-sm font-medium dark:text-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200">
                                Majetkový účet
                            </div>
                        </li>
                    </ul>
                </li>
                <li>
                    <div hx-get="/prehlady/poplatky" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium text-gray-900 rounded-lg dark:text-gray-100 dark:hover:bg-gray-600 cursor-pointer transition duration-75 group hover:bg-gray-200">
                        Poplatky
                    </div>
                </li>
                <li>
                    <div hx-get="/prehlady/vyplatene-vynosy" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium text-gray-900 rounded-lg dark:text-gray-100 dark:hover:bg-gray-600 cursor-pointer transition duration-75 group hover:bg-gray-200">
                        Vyplatené výnosy
                    </div>
                </li>
                <li>
                    <div hx-get="/prehlady/nbs-gfi" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center text-sm font-medium cursor-pointer text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700">
                        NBS a GFI
                    </div>
                </li>
                <li>
                    <div hx-get="/prehlady/zmluvy-klienti" hx-target="#pageContentMain" hx-replace-url="true"
                        onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                        class="sub flex items-center cursor-pointer text-sm font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700">
                        Zmluvy a klienti
                    </div>
                </li>
            </ul>
        </li>
        <li>
            <a class="flex gap-4 items-center topLevelNavigation transition-all p-1 px-2 text-sm font-medium dark:text-gray-100 text-gray-900 rounded-lg dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700 group"
                href="/obchodny-dennik">
                <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 10h16m-8-3V4M7 7V4m10 3V4M5 20h14a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Zm3-7h.01v.01H8V13Zm4 0h.01v.01H12V13Zm4 0h.01v.01H16V13Zm-8 4h.01v.01H8V17Zm4 0h.01v.01H12V17Zm4 0h.01v.01H16V17Z" />
                </svg>
                <span class="flex-1 whitespace-nowrap">Obchodný denník</span>
            </a>
        </li>
        <li>
            <a class="flex items-center topLevelNavigation p-1 px-2 bg-red-200 dark:bg-red-800 gap-4 transition-all text-sm font-medium text-red-900 dark:text-red-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 group"
                href="#">
                <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 13V8m0 8h.01M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>

                <span class="flex-1 whitespace-nowrap">Risk Manažment</span>
            </a>
        </li>
        <li>
            <div id="uzavierkamneu" hx-get="/uzavierka" hx-target="#pageContentMain" hx-replace-url="true"
                hx-push-url="true" onclick="refreshMenu(event); refreshTopBar(event); refreshBreadcrumb(event);"
                class="flex items-center topLevelNavigation p-1 px-2 gap-4 transition-all text-sm font-medium text-red-900 rounded-lg dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700 group">
                <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
                <span class="flex-1 text-left whitespace-nowrap">Uzávierka</span>
            </div>
        </li>
        <!-- <li>
            <button aria-controls="dropdown-pages"
                class="flex items-center topLevelNavigation p-1 bg-red-200 dark:bg-red-800 px-2 gap-4 w-full text-sm font-medium text-red-900 dark:text-red-200 rounded-lg transition duration-75 group hover:bg-gray-200 dark:hover:bg-gray-600"
                data-collapse-toggle="uctovnictvo-pages" type="button">
                <svg class="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 19V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v13H7a2 2 0 0 0-2 2Zm0 0a2 2 0 0 0 2 2h12M9 3v14m7 0v4" />
                </svg>
                <span class="flex-1 text-left whitespace-nowrap">Účtovníctvo</span>
                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="m8 10 4 4 4-4" />
                </svg>
            </button>
            <ul class="hidden py-2 space-y-2" id="uctovnictvo-pages">
                <li>
                    <a class="sub flex items-center text-sm font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700"
                        href="#">Kurzové rozdiely</a>
                </li>
                <li>
                    <a class="sub flex flex-col text-sm font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200"
                        href="#">Podsúvaha <small class="text-gray-500">(wip)</small></a>
                </li>
            </ul>
        </li> -->
    </ul>
    <hr class="mt-3 border-gray-300 dark:border-gray-600" />
    <small class="text-gray-400 dark:text-gray-500">Všeobecné odkazy</small>
    <ul class="pt-2 mt-1 space-y-2">
        <li>
            <a href="https://sympatia.gitbook.io/goldmann/" target="_blank"
                class="flex items-center topLevelNavigation gap-3 p-1 px-2 text-sm font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-200 dark:hover:bg-gray-700 dark:text-white group">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-book-open-text">
                    <path d="M12 7v14" />
                    <path d="M16 12h2" />
                    <path d="M16 8h2" />
                    <path
                        d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z" />
                    <path d="M6 12h2" />
                    <path d="M6 8h2" />
                </svg>
                <span>Dokumentácia</span>
            </a>
        </li>
        <li>
            <button aria-controls="dropdown-pages"
                class="flex items-center gap-3 topLevelNavigation p-1 px-2 w-full text-sm font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-200 dark:text-white dark:hover:bg-gray-700"
                type="button">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide text-gray-800 dark:text-gray-200 lucide-users">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
                <span>Klientská zóna</span>
            </button>
        </li>
        <li>
            <a href="https://app.clickup.com/t/86995eez2" target="_blank"
                class="flex items-center gap-3 topLevelNavigation py-1 px-2 text-sm w-full font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-200 dark:hover:bg-gray-700 dark:text-white group">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide text-red-500 lucide-message-circle-question">
                    <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z" />
                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                    <path d="M12 17h.01" />
                </svg>
                <span>Podpora</span>
            </a>
        </li>
        <li>
            <button
                class="flex items-center gap-2 topLevelNavigation text-sm w-full font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-200 dark:hover:bg-gray-700 dark:text-white group">
                <img src="/src/assets/img/clickup.svg" alt="clickup-logo" width="30" />
                <span>ClickUp aktualizácia</span>
            </button>
        </li>
    </ul>
</div>