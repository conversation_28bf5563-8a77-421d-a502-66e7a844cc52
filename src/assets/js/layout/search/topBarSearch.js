$("#topbar-search").focus((e) => {
    $("#topbar-searchWrapper").animate({ scale: 1.05 }, 100);
    $(".globalSearchResults").animate({ height: '90vh' }, 300);
});
$("#topbar-search").focusout((e) => {
    $("#topbar-searchWrapper").animate({ scale: 1 }, 100);
    $(".globalSearchResults").animate({ height: '0' }, 300);
});

function openNotifications(e) {
    e.stopPropagation();
    $("#notification-dropdown").animate({ right: "1rem" }, 200);
    $("#notificationDropdownOverlay").css("z-index", "40");
    $("#notificationDropdownOverlay").css("width", "100%");
    $("#notificationDropdownOverlay").css("height", "100vh");
    $("#notificationDropdownOverlay").animate({ opacity: 1 }, 200);
}

function openUserMenu(e) {
    e.stopPropagation();
    $("#usersidebar-dropdown").animate({ right: "1rem" }, 200);
    $("#profileOverlay").css("z-index", "40");
    $("#profileOverlay").css("width", "100%");
    $("#profileOverlay").css("height", "100vh");
    $("#profileOverlay").animate({ opacity: 1 }, 200);
}

$("#usersidebar-dropdown").on("click", (e) => {
    if (e.target.id !== "closeUserDropdown") {
        e.stopPropagation();
    }
});

$("#notification-dropdown").on("click", (e) => {
    console.log(e.target);
    if (e.target.id !== "closeNotificationDropdown" || e.target.id !== "markReadBtn") {
        e.stopPropagation();
    }
    if (e.target.id !== "closeUserDropdown") {
        e.stopPropagation();
    }
});

$("#closeUserDropdown").on("click", (e) => {
    $("#notification-dropdown").animate({ right: "-25rem" }, 200);
    $("#profileOverlay").animate({ opacity: 0 }, 100);
    setTimeout(() => {
        $("#profileOverlay").css("z-index", "0");
        $("#profileOverlay").css("width", "0");
        $("#profileOverlay").css("height", "0");
    }, 300);
});

$("#closeNotificationDropdown").on("click", (e) => {
    $("#notification-dropdown").animate({ right: "-25rem" }, 200);
    $("#notificationDropdownOverlay").animate({ opacity: 0 }, 100);
    setTimeout(() => {
        $("#notificationDropdownOverlay").css("z-index", "0");
        $("#notificationDropdownOverlay").css("width", "0");
        $("#notificationDropdownOverlay").css("height", "0"); s
    }, 300);
});

$(document).click(function (event) {
    let obj = $(".notification-dropdown");
    if (!obj.is(event.target) && !obj.has(event.target).length) {
        $("#notification-dropdown").animate({ right: "-25rem" }, 200);
        $("#notificationDropdownOverlay").animate({ opacity: 0 }, 100);
        setTimeout(() => {
            $("#notificationDropdownOverlay").css("z-index", "0");
            $("#notificationDropdownOverlay").css("width", "0");
            $("#notificationDropdownOverlay").css("height", "0");
        }, 300);
    }
    let usr = $("#usersidebar-dropdown");
    if (!usr.is(event.target) && !usr.has(event.target).length) {
        $("#usersidebar-dropdown").animate({ right: "-25rem" }, 200);
        $("#profileOverlay").animate({ opacity: 0 }, 100);
        setTimeout(() => {
            $("#profileOverlay").css("z-index", "0");
            $("#profileOverlay").css("width", "0");
            $("#profileOverlay").css("height", "0");
        }, 300);
    }
});

$("#markReadBtn").on("click", (e) => {
    $("#markReadIcon").css("display", "none");
    $("#markReadSpinner").css("display", "block");
})

document.body.addEventListener('htmx:afterSettle', function (evt) {
    $("#markReadIcon").css("display", "block");
    $("#markReadSpinner").css("display", "none");
    const notifCount = $("#notifCountHiddenAfter").html();
    console.log(notifCount);
    $("#notification-dot").html(notifCount);
    $("#notifCount").html(notifCount);
    if (parseInt(notifCount) === 0) {
        $("#notification-dot").css("display", "none");
    }
});

async function refreshMenu(e) {
    e.stopPropagation();
    htmx.ajax('POST', '/refreshMenu', {
        target: "#drawer-navigation-wrapper",
        values: {
            path: e.currentTarget.attributes[1].nodeValue
        },
    });
}

async function refreshTopBar(e) {
    htmx.ajax('POST', '/refreshTopBar', {
        target: "#topbar-wrapper",
        source: "#topbar-wrapper",
        sync: "queue first",
        values: {
            path: e.currentTarget.attributes[1].nodeValue
        },
    });
}

async function refreshBreadcrumb(e) {
    e.stopPropagation();
    htmx.ajax('POST', '/breadcrumb', {
        target: "#appBreadCrumb",
        source: "#appBreadCrumb",
        values: {
            path: e.currentTarget.attributes[0].nodeValue
        },
    });
}

async function refreshMenuHome() {
    htmx.ajax('POST', '/refreshMenu', {
        target: "#drawer-navigation-wrapper",
        source: "#drawer-navigation-wrapper",
        values: {
            path: "/"
        },
        sync: "queue last"
    });
}