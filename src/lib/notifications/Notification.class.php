<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";

class Notification
{
    protected int $activityid;
    protected string $destinacia;
    protected int $objektid;
    protected string $action;
    protected int $userid;
    protected string $username;
    protected string $datetimeactivity;
    protected string $message;
    protected string $type;
    protected string $notificationCode;
    protected bool $created;
    protected array $subjectDetails;
    protected bool|int $needsMentioning;
    protected string|null $objektDetail;
    protected null|int|string $fondid;

    function __construct($activityid, $destinacia, $objektid, $action, $userid, $username, $notificationCode, $objektDetail, $needsMentioning, $fondid, $type = "system", $message = "Toto je notifikácia")
    {
        $this->activityid = $activityid;
        $this->destinacia = $destinacia;
        $this->objektid = $objektid;
        $this->action = $action;
        $this->userid = $userid;
        $this->username = $username;
        $this->notificationCode = $notificationCode;
        $this->objektDetail = $objektDetail;
        $this->fondid = $fondid;
        $this->type = $type;
        if ($needsMentioning) {
            $this->needsMentioning = true;
        } else {
            $this->needsMentioning = 0;
        }
        $this->message = $message;
    }

    function setDateTime()
    {
        $date = new DateTime("now", new DateTimeZone('Europe/Bratislava'));
        $this->datetimeactivity = $date->format('Y-m-d H:i:s T');
    }

    function setCreated($created)
    {
        $this->created = $created;
    }

    public function createNotifcation()
    {
        $this->setDateTime();
        $notificationID = Connection::getDataFromDatabase("SELECT nextval('s_notification_id')", defaultDB)[1][0]["nextval"];
        $jes = Connection::InsertUpdateCreateDelete("INSERT INTO notifications (id, activityid, destinacia, objektid, action, userid, datetimeactivity, message, type, notification, objektDetail, needsmentioning, fondid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)", [
            $notificationID,
            $this->activityid,
            $this->destinacia,
            $this->objektid,
            $this->action,
            $this->userid,
            $this->datetimeactivity,
            $this->message,
            $this->type,
            $this->notificationCode,
            $this->objektDetail,
            $this->needsMentioning,
            $this->fondid
        ], defaultDB);
        if (gettype($jes) !== "integer") {
            $this->setCreated(false);
            echo $jes;
        } else {
            $this->setCreated(true);
            return $notificationID;
        }
        
    }
}