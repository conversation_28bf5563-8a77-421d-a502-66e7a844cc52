<?php
require_once "/home/<USER>/www/src/lib/connection.php";
require_once "/home/<USER>/www/conf/settings.php";
require_once "/home/<USER>/www/src/lib/notifications/Notification.class.php";
class RenderNotification extends Notification
{
    function getNotificationColor()
    {
        switch ($this->action) {
            case "create":
                return "#31C48D";
            case "delete":
                return "#F05252";
        }
    }

    function ifColumnExistsInTable($column, $table): bool
    {
        $columnExists = Connection::getDataFromDatabase("SELECT column_name FROM information_schema.columns WHERE table_name = '$table' AND column_name = '$column'", defaultDB)[1];
        return sizeof($columnExists) > 0;
    }

    function getNotificationsDetailsOLD($notificationID)
    {
        $notifDetail = Connection::getDataFromDatabase("SELECT * FROM processlog WHERE logid = $notificationID", defaultDB)[1][0];
        $date = $notifDetail["datetimeactivity"];
        if ($this->destinacia === "uzavierka") {
            $this->destinacia = "uzavierkalog";
            $column = "fondid";
            $dateQuery = "AND datetimeactivity = '$date'";
        } else {
            $column = "subjektid";
            $dateQuery = "";
        }

        $detail = Connection::getDataFromDatabase("SELECT * FROM $this->destinacia WHERE $column = $this->fondid $dateQuery", defaultDB)[1];
        if (sizeof($detail) > 0 || $detail != NULL) {
            foreach ($detail[0] as $key => $item) {
                $this->subjectDetails[$key] = $item;
            }
        } else {
            $this->subjectDetails = [];
        }
    }

    function getNotificationsDetails($notificationID)
    {
        $notifDetail = Connection::getDataFromDatabase("SELECT * FROM notifications WHERE id = $notificationID", defaultDB)[1][0]["objektdetail"];
        $notifDetail = json_decode($notifDetail);
        $column = $notifDetail[0];
        $data = "'$notifDetail[1]'";

        if ($this->ifColumnExistsInTable("fondid", $this->destinacia)) {
            $fondQuery = " AND (subjektid = $this->fondid OR fondid = $this->fondid)";
        } else {
            if ($this->ifColumnExistsInTable("subjektid", $this->destinacia)) {
                $fondQuery = " OR subjektid = $this->fondid";
            } else {
                $fondQuery = "";
            }
        }

        if ($this->ifColumnExistsInTable("datetimeactivity", $this->destinacia)) {
            $dateQuery = " AND datetimeactivity = '" . $notifDetail["datetimeactivity"] . "'";
        } else {
            $dateQuery = "";
        }

        if ($this->ifColumnExistsInTable("obratdatetime", $this->destinacia)) {
            $dateQuery = " AND obratdatetime = '" . $notifDetail["datetimeactivity"] . "'";
        } else {
            $dateQuery = "";
        }

        $detail = Connection::getDataFromDatabase("SELECT * FROM $this->destinacia WHERE $column = $data $fondQuery $dateQuery", defaultDB)[1];
        if (sizeof($detail) > 0 || $detail != NULL) {
            foreach ($detail[0] as $key => $item) {
                $this->subjectDetails[$key] = $item;
            }
        } else {
            $this->subjectDetails = [];
        }
    }

    /**
     * Creation of notification body with descriptive text and dynamic objects like links.
     * @return void
     */
    function createNotificationDynamicBody($notificationID): string
    {
        $notificationBody = "";
        //echo "notification code: " . $this->notificationCode;
        if($this->subjectDetails === []){
            return $this->message;
        }
        switch ($this->notificationCode) {
            case "sanctionCreate":
                $notificationBody = "Používateľ $this->username vytvoril sankciu pre cenný papier <strong>" . $this->subjectDetails["isin"] . "</strong>. 
                Ako dôvod uviedol <span>" . $this->subjectDetails["reason"] . "</span>";
                break;
            case "sanctionDelete":
                $notificationBody = "Používateľ $this->username odstránil sankciu pre cenný papier <strong>" . $this->subjectDetails["isin"] . "</strong>.";
                break;
            case "zamerCPdelete":
                $notificationBody = "Odstránil investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na ";
                if ($this->subjectDetails["druhobchodu"] === "nakup") {
                    $notificationBody .= "nákup";
                } else if ($this->subjectDetails["druhobchodu"] === "predaj") {
                    $notificationBody .= "predaj";
                }
                switch ($this->subjectDetails["eqid"]) {
                    case "Bonds":
                        $notificationBody .= " dlhopisu ";
                        break;
                    case "Shares":
                        $notificationBody .= " akcie ";
                        break;
                    case "Fonds":
                        $notificationBody .= " fondu ";
                        break;
                }
                $notificationBody .= " <a href='/aktiva/cenne-papiere/detail/" . $this->subjectDetails["isin"] . "' class='font-medium text-blue-600 underline dark:text-blue-500 hover:no-underline'>" . $this->subjectDetails["isin"] . "</a>";
                break;
            case "zamerKTVdelete":
                $notificationBody = "Odstránil investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na termínovaný vklad o hodnote";
                $notificationBody .= " <span class='font-medium text-blue-600 dark:text-blue-500'>" . number_format($this->subjectDetails["sum_td"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</span>";
                break;
            case "zamerKonvdelete":
                $notificationBody = "Odstránil investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na konverziu z ";
                $notificationBody .= "<span class='bg-gray-100 text-gray-800 rounded-md text-xs font-medium px-2.5 py-0.5 dark:bg-gray-700 dark:text-gray-400 border border-gray-500'>" . $this->subjectDetails["menadebet"] . " (" . number_format($this->subjectDetails["sumadebet"], 2, ",", " ") . ")" . "</span>";
                $notificationBody .= " na ";
                $notificationBody .= "<span class='bg-gray-100 text-gray-800 rounded-md text-xs font-medium px-2.5 py-0.5 dark:bg-gray-700 dark:text-gray-400 border border-gray-500'>" . $this->subjectDetails["menakredit"] . " (" . number_format($this->subjectDetails["sumakredit"], 2, ",", " ") . ")" . "</span>";
                break;
            case "zamerKonvcreate":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                $notificationBody = "Vytvoril investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na konverziu z ";
                $notificationBody .= "<span class='bg-gray-100 text-gray-800 rounded-md text-xs font-medium px-2.5 py-0.5 dark:bg-gray-700 dark:text-gray-400 border border-gray-500'>" . $this->subjectDetails["menadebet"] . " (" . number_format($this->subjectDetails["sumadebet"], 2, ",", " ") . ")" . "</span>";
                $notificationBody .= " na ";
                $notificationBody .= "<span class='bg-gray-100 text-gray-800 rounded-md text-xs font-medium px-2.5 py-0.5 dark:bg-gray-700 dark:text-gray-400 border border-gray-500'>" . $this->subjectDetails["menakredit"] . " (" . number_format($this->subjectDetails["sumakredit"], 2, ",", " ") . ")" . "</span>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "zamerKonvRekonf":
            case "zamerKonvToKonf":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                if ($this->notificationCode === "zamerKonvRekonf") {
                    $notificationBody .= "<b>Rekonfirmoval</b>";
                } else {
                    $notificationBody .= "<b>Konfirmoval</b>";
                }
                $notificationBody .= " investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong><span> na konverziu z </span>";
                $notificationBody .= "<span class='bg-gray-100 w-full text-gray-800 rounded-md text-xs font-medium px-2.5 py-0.5 dark:bg-gray-700 dark:text-gray-400 border border-gray-500'>" . $this->subjectDetails["menadebet"] . " (" . number_format($this->subjectDetails["sumadebet"], 2, ",", " ") . ")" . "</span>";
                $notificationBody .= " na ";
                $notificationBody .= "<span class='bg-gray-100 text-gray-800 rounded-md text-xs font-medium px-2.5 py-0.5 dark:bg-gray-700 dark:text-gray-400 border border-gray-500'>" . $this->subjectDetails["menakredit"] . " (" . number_format($this->subjectDetails["sumakredit"], 2, ",", " ") . ")" . "</span>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "zamerCPCreate":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                $notificationBody = "Vytvoril investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na ";
                if ($this->subjectDetails["druhobchodu"] === "nakup") {
                    $notificationBody .= "nákup";
                } else if ($this->subjectDetails["druhobchodu"] === "predaj") {
                    $notificationBody .= "predaj";
                }
                switch ($this->subjectDetails["eqid"]) {
                    case "Bonds":
                        $notificationBody .= " dlhopisu ";
                        break;
                    case "Shares":
                        $notificationBody .= " akcie ";
                        break;
                    case "Fonds":
                        $notificationBody .= " fondu ";
                        break;
                }
                $notificationBody .= " <a href='/aktiva/cenne-papiere/detail/" . $this->subjectDetails["isin"] . "' class='font-semibold dark:text-gray-900 text-blue-600 underline dark:text-blue-500 hover:no-underline'>" . $this->subjectDetails["isin"] . "</a>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "zamerCPRekonf":
            case "zamerCPToKonf":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                if ($this->notificationCode === "zamerCPRekonf") {
                    $notificationBody .= "<b>Rekonfirmoval</b>";
                } else {
                    $notificationBody .= "<b>Konfirmoval</b>";
                }
                $notificationBody .= " investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na ";
                if ($this->subjectDetails["druhobchodu"] === "nakup") {
                    $notificationBody .= "nákup";
                } else if ($this->subjectDetails["druhobchodu"] === "predaj") {
                    $notificationBody .= "predaj";
                }
                switch ($this->subjectDetails["eqid"]) {
                    case "Bonds":
                        $notificationBody .= " dlhopisu ";
                        break;
                    case "Shares":
                        $notificationBody .= " akcie ";
                        break;
                    case "Fonds":
                        $notificationBody .= " fondu ";
                        break;
                }
                $notificationBody .= " <a href='/aktiva/cenne-papiere/detail/" . $this->subjectDetails["isin"] . "' class='font-medium text-blue-600 underline dark:text-gray-800 font-bold hover:no-underline'>" . $this->subjectDetails["isin"] . "</a>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "zamerKTVcreate":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                $notificationBody = "Vytvoril investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na termínovaný vklad o hodnote ";
                $notificationBody .= "<span class='font-bold dark:text-gray-800 text-blue-600'> " . number_format($this->subjectDetails["sum_td"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</span>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "zamerKTVRekonf":
            case "zamerKTVToKonf":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                if ($this->notificationCode === "zamerKTVRekonf") {
                    $notificationBody .= "<b>Rekonfirmoval</b>";
                } else {
                    $notificationBody .= "<b>Konfirmoval</b>";
                }
                $notificationBody .= " investičný zámer č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["dealid"] . "</strong> na termínovaný vklad o hodnote ";
                $notificationBody .= "<span class='font-bold dark:text-gray-800 text-blue-600'> " . number_format($this->subjectDetails["sum_td"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</span>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "dlhopisConfirmNominalu":
            case "dlhopisConfirmKuponu":
                $object = $this->notificationCode === "dlhopisSplatenieKuponu" ? "kupón" : "istinu";
                $notificationBody = "Potvrdil $object dlhopisu <a href='/aktiva/cenne-papiere/detail/" . $this->subjectDetails["kodaktiva"] . "' class='font-bold text-blue-600 underline dark:text-gray-200 hover:no-underline'>" . $this->subjectDetails["kodaktiva"] . "</a>";
                break;
            case "dlhopisSplatenieNominalu":
            case "dlhopisSplatenieKuponu":
                $subactivities = Connection::getDataFromDatabase("SELECT DISTINCT po.podielnikid, p.meno, p.prieznaz, a.activitypopis, po.cislozmluvy
                    FROM subactivities s
                            INNER JOIN portfolio po ON po.fondid = s.fondid
                            INNER JOIN podielnik p ON p.podielnikid = po.podielnikid
                            INNER JOIN activity a ON a.activityid = s.activityid AND a.destinacia = s.destinacia
                    WHERE s.notificationid = $notificationID", defaultDB)[1];
                $object = $this->notificationCode === "dlhopisSplatenieKuponu" ? "kupón" : "istinu";
                $notificationBody .= "Splatil " . $object . " dlhopisu <a href='/aktiva/cenne-papiere/detail/" . $this->subjectDetails["kodaktiva"] . "' class='font-bold text-blue-600 underline dark:text-gray-200 hover:no-underline'>" . $this->subjectDetails["kodaktiva"] . "</a>";
                if (sizeof($subactivities) > 0) {
                    $notificationBody .= '<div id="accordion-collapse" data-accordion="collapse">
                            <h2 id="accordion-collapse-heading-1">
                                <button type="button" class="flex items-center text-sm justify-between w-full font-medium rtl:text-right hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-900 gap-3 p-1 rounded-t-md mt-2 px-2 transition-all text-gray-500 dark:text-gray-400" data-accordion-target="#accordion-collapse-body-1" aria-expanded="false" aria-controls="accordion-collapse-body-1">
                                <span>Všetky vykonané aktivity</span>
                                <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                                </svg>
                                </button>
                            </h2>';
                    $notificationBody .= '<div id="accordion-collapse-body-1" class="hidden" aria-labelledby="accordion-collapse-heading-1">';
                    foreach ($subactivities as $subactivity) {
                        $notificationBody .= '
                                <div class="p-2 dark:bg-gray-900">
                                    <p class="text-gray-500 dark:text-gray-400">' . $subactivity["activitypopis"] . ' pre klienta <strong class="underline hover:no-underline cursor-pointer" hx-get="/klienti/detail/' . $subactivity["podielnikid"] . '" hx-target="#pageContentMain" hx-replace-url="true">' . $subactivity["meno"] . " " . $subactivity["prieznaz"] . '</strong> <a href="/klienti/detail/' . $subactivity["podielnikid"] . '#' . $subactivity["cislozmluvy"] . '" class="underline hover:no-underline cursor-pointer">[' . $subactivity["cislozmluvy"] . ']</a></p>
                                </div>';
                    }
                    $notificationBody .= "</div></div>";
                }
                break;
            case "deletePrikaz":
                $notificationBody = "Stornoval natipovanie príkazu na úhradu č. <strong>" . $this->subjectDetails["id"] . "</strong>. ";
                break;
            case "natipovaniePrikazu":
                $notificationBody = "Natipoval príkaz na úhradu č. <strong>" . $this->subjectDetails["id"] . "</strong>. ";
                break;
            case "zmenaSumyPoplatokGlobal":
                $subactivities = Connection::getDataFromDatabase("SELECT DISTINCT po.podielnikid, p.meno, p.prieznaz, po.cislozmluvy, s.detailedmessage
                    FROM subactivities s
                            INNER JOIN portfolio po ON po.fondid = s.fondid
                            INNER JOIN podielnik p ON p.podielnikid = po.podielnikid
                    WHERE s.notificationid = $notificationID", defaultDB)[1];
                $notificationBody = "Zmenil sumu pri viacerých poplatkoch. Detaily nižšie:";
                if (sizeof($subactivities) > 0) {
                    $notificationBody .= '<div id="accordion-collapse-' . $notificationID . '" data-accordion="collapse">
                            <h2 id="accordion-collapse-heading-' . $notificationID . '">
                                <button type="button" class="flex items-center text-sm justify-between w-full font-medium rtl:text-right hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-900 gap-3 p-1 rounded-t-md mt-2 px-2 transition-all text-gray-500 dark:text-gray-400" data-accordion-target="#accordion-collapse-body-' . $notificationID . '" aria-expanded="false" aria-controls="accordion-collapse-body-' . $notificationID . '">
                                <span>Všetky poplatky (' . sizeof($subactivities) . ')</span>
                                <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                                </svg>
                                </button>
                            </h2>';
                    $notificationBody .= '<div id="accordion-collapse-body-' . $notificationID . '" class="hidden dark:bg-gray-900 p-2 flex flex-col gap-2" aria-labelledby="accordion-collapse-heading-1">';
                    foreach ($subactivities as $subactivity) {
                        $notificationBody .= '
                                    <div class="p-2 dark:bg-gray-800 rounded-md text-xs">
                                        <p class="text-gray-500 dark:text-gray-400">' . $subactivity["detailedmessage"] . '. Vykonané pre klienta <strong class="underline hover:no-underline cursor-pointer" hx-get="/klienti/detail/' . $subactivity["podielnikid"] . '" hx-target="#pageContentMain" hx-replace-url="true">' . $subactivity["meno"] . " " . $subactivity["prieznaz"] . '</strong> <a href="/klienti/detail/' . $subactivity["podielnikid"] . '#' . $subactivity["cislozmluvy"] . '" class="underline hover:no-underline cursor-pointer">[' . $subactivity["cislozmluvy"] . ']</a></p>
                                    </div>';
                    }
                    $notificationBody .= "</div></div>";
                }
                break;
            case "tvorbaPoplatku":
                $notificationBody = "Vytvoril poplatok č. <strong>" . $this->subjectDetails["id"] . "</strong>. ";
                $notificationBody .= "Poplatok bol vytvorený so sumou <strong>" . number_format($this->subjectDetails["suma"], 2, ".", " ") . " " . $this->subjectDetails["mena"] . "</strong>  a bol mu priradený účet <strong>" . $this->subjectDetails["cub"] . '</strong>';
                break;
            case "rekonfPoplatok":
                $notificationBody = "Rekonfirmoval poplatok č. <strong>" . $this->subjectDetails["id"] . "</strong>. ";
                $notificationBody .= "Poplatok bol rekonfirmovaný so sumou <strong>" . number_format($this->subjectDetails["suma"], 2, ".", " ") . " " . $this->subjectDetails["mena"] . "</strong>  a bol mu priradený účet <strong>" . $this->subjectDetails["cub"] . '</strong>';
                break;
            case "splateniePoplatok":
                $notificationBody = "Splatil poplatok č. <strong>" . $this->subjectDetails["id"] . "</strong>. ";
                $notificationBody .= "Poplatok bol splatený so sumou <strong>" . number_format($this->subjectDetails["suma"], 2, ".", " ") . " " . $this->subjectDetails["mena"] . "</strong>";
                break;
            case "rekonfPoplatkuGlobal":
                $subactivities = Connection::getDataFromDatabase("SELECT DISTINCT po.podielnikid, p.meno, p.prieznaz, po.cislozmluvy, s.detailedmessage
                        FROM subactivities s
                                INNER JOIN portfolio po ON po.fondid = s.fondid
                                INNER JOIN podielnik p ON p.podielnikid = po.podielnikid
                        WHERE s.notificationid = $notificationID", defaultDB)[1];
                $notificationBody = "Rekonfirmoval viaceré poplatky. Detaily nižšie:";
                if (sizeof($subactivities) > 0) {
                    $notificationBody .= '<div id="accordion-collapse-' . $notificationID . '" data-accordion="collapse">
                                <h2 id="accordion-collapse-heading-' . $notificationID . '">
                                    <button type="button" class="flex items-center text-sm justify-between w-full bg-gray-100 font-medium rtl:text-right hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-900 gap-3 p-1 rounded-t-md mt-2 px-2 transition-all text-gray-500 dark:text-gray-400" data-accordion-target="#accordion-collapse-body-' . $notificationID . '" aria-expanded="false" aria-controls="accordion-collapse-body-' . $notificationID . '">
                                    <span>Všetky poplatky (' . sizeof($subactivities) . ')</span>
                                    <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                                    </svg>
                                    </button>
                                </h2>';
                    $notificationBody .= '<div id="accordion-collapse-body-' . $notificationID . '" class="hidden dark:bg-gray-900 p-2 flex flex-col gap-2" aria-labelledby="accordion-collapse-heading-1">';
                    foreach ($subactivities as $subactivity) {
                        $notificationBody .= '
                                        <div class="p-2 dark:bg-gray-800 rounded-md text-xs">
                                            <p class="text-gray-500 dark:text-gray-400">' . $subactivity["detailedmessage"] . '. Vykonané pre klienta <strong class="underline hover:no-underline cursor-pointer" hx-get="/klienti/detail/' . $subactivity["podielnikid"] . '" hx-target="#pageContentMain" hx-replace-url="true">' . $subactivity["meno"] . " " . $subactivity["prieznaz"] . '</strong> <a href="/klienti/detail/' . $subactivity["podielnikid"] . '#' . $subactivity["cislozmluvy"] . '" class="underline hover:no-underline cursor-pointer">[' . $subactivity["cislozmluvy"] . ']</a></p>
                                        </div>';
                    }
                    $notificationBody .= "</div></div>";
                }
                break;
            case "terminationPoplatokGlobal":
                $subactivities = Connection::getDataFromDatabase("SELECT DISTINCT po.podielnikid, p.meno, p.prieznaz, po.cislozmluvy, s.detailedmessage
                            FROM subactivities s
                                    INNER JOIN portfolio po ON po.fondid = s.fondid
                                    INNER JOIN podielnik p ON p.podielnikid = po.podielnikid
                            WHERE s.notificationid = $notificationID", defaultDB)[1];
                $notificationBody = "Vygenervoal poplatky k ukončeniu zmluvy. Detaily nižšie:";
                if (sizeof($subactivities) > 0) {
                    $notificationBody .= '<div id="accordion-collapse-' . $notificationID . '" data-accordion="collapse">
                                    <h2 id="accordion-collapse-heading-' . $notificationID . '">
                                        <button type="button" class="flex items-center text-sm justify-between w-full bg-gray-100 font-medium rtl:text-right hover:bg-gray-100 dark:bg-gray-700 dark:hover:bg-gray-900 gap-3 p-1 rounded-t-md mt-2 px-2 transition-all text-gray-500 dark:text-gray-400" data-accordion-target="#accordion-collapse-body-' . $notificationID . '" aria-expanded="false" aria-controls="accordion-collapse-body-' . $notificationID . '">
                                        <span>Všetky poplatky (' . sizeof($subactivities) . ')</span>
                                        <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
                                        </svg>
                                        </button>
                                    </h2>';
                    $notificationBody .= '<div id="accordion-collapse-body-' . $notificationID . '" class="hidden dark:bg-gray-900 p-2 flex flex-col gap-2" aria-labelledby="accordion-collapse-heading-1">';
                    foreach ($subactivities as $subactivity) {
                        $notificationBody .= '
                                            <div class="p-2 dark:bg-gray-800 rounded-md text-xs">
                                                <p class="text-gray-500 dark:text-gray-400">' . $subactivity["detailedmessage"] . '. Vykonané pre klienta <strong class="underline hover:no-underline cursor-pointer" hx-get="/klienti/detail/' . $subactivity["podielnikid"] . '" hx-target="#pageContentMain" hx-replace-url="true">' . $subactivity["meno"] . " " . $subactivity["prieznaz"] . '</strong> <a href="/klienti/detail/' . $subactivity["podielnikid"] . '#' . $subactivity["cislozmluvy"] . '" class="underline hover:no-underline cursor-pointer">[' . $subactivity["cislozmluvy"] . ']</a></p>
                                            </div>';
                    }
                    $notificationBody .= "</div></div>";
                }
                break;
            case "presunCreate":
                $notificationBody = "Vytvoril zámer na presun prostriedkov s označením <strong hx-get='/zoznam-transakcii/detail/" . $this->subjectDetails["dealid"] . "' class='underline hover:no-underline cursor-pointer' hx-target='#pageContentMain' hx-replace-url='true'>"
                    . $this->subjectDetails["dealid"] . "</strong> z účtu <strong>" . $this->subjectDetails["ucet"] . "</strong> na účet <strong>" . $this->subjectDetails["externy_ucet"] . "</strong> v sume <strong>"
                    . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</strong>";
                break;
            case "potvrdPrevod":
                $notificationBody = "Potvrdil a vykonal presun prostriedkov s označením <strong hx-get='/zoznam-transakcii/detail/" . $this->subjectDetails["dealid"] . "' class='underline hover:no-underline cursor-pointer' hx-target='#pageContentMain' hx-replace-url='true'>"
                    . $this->subjectDetails["dealid"] . "</strong> z účtu <strong>" . $this->subjectDetails["ucet"] . "</strong> na účet <strong>" . $this->subjectDetails["externy_ucet"] . "</strong> v sume <strong>"
                    . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</strong>";
                break;
            case "deletePresunProstriedkovHelper":
            case "deletePresunProstriedkov":
                $notificationBody = "Zrušil zámer na presun prostriedkov s označením <strong hx-get='/zoznam-transakcii/detail/" . $this->subjectDetails["dealid"] . "' class='underline hover:no-underline cursor-pointer' hx-target='#pageContentMain' hx-replace-url='true'>"
                    . $this->subjectDetails["dealid"] . "</strong> z účtu <strong>" . $this->subjectDetails["ucet"] . "</strong> na účet <strong>" . $this->subjectDetails["externy_ucet"] . "</strong> v sume <strong>"
                    . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</strong>";
                break;
            case "createPoplatok":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                $notificationBody = "Vytvoril nový poplatok č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["id"] . "</strong> v sume ";
                $notificationBody .= "<span class='font-bold dark:text-gray-800 text-blue-600'> " . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</span>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "rekonfPoplatku":
                if ($this->fondid != null || $this->fondid != 0) {
                    $client = Connection::getDataFromDatabase("SELECT meno, prieznaz, p.podielnikid FROM podielnik p JOIN portfolio po ON po.fondid = $this->fondid AND p.podielnikid = po.podielnikid", defaultDB)[1][0];
                }
                $notificationBody = "Rekonfirmoval poplatok č. ";
                $notificationBody .= " <strong>" . $this->subjectDetails["id"] . "</strong> v sume ";
                $notificationBody .= "<span class='font-bold dark:text-gray-800 text-blue-600'> " . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</span>";
                if ($this->fondid !== null || $this->fondid !== 0) {
                    $notificationBody .= " pre užívateľa <strong class='underline hover:no-underline cursor-pointer' hx-get='/klienti/detail/" . $client["podielnikid"] . "' hx-target='#pageContentMain' hx-replace-url='true'>" . $client["meno"] . " " . $client["prieznaz"] . "</strong>";
                }
                break;
            case "konfirmaciapp":
                $activityid = $this->subjectDetails["logactivityid"];
                $destinacia = $this->destinacia;
                $pouzivatel = $this->getInitiatorName();
                $activity = Connection::getDataFromDatabase("SELECT activitypopis FROM activity WHERE activityid = $activityid AND destinacia = '$destinacia'", defaultDB)[1][0]["activitypopis"];
                $notificationBody = $pouzivatel["username"] . " vykonal akciu <strong>" . $activity . "</strong> Táto akcia bola vykonaná s číslom <strong>" . $this->subjectDetails["dealid"] . "</strong> a sumou <strong>" . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</strong>";
                break;
            case "uzavierka":
                $activityid = $this->subjectDetails["activityid"];
                $destinacia = "uzavierka";
                $activity = Connection::getDataFromDatabase("SELECT activitypopis FROM activity WHERE activityid = $activityid AND destinacia = '$destinacia'", defaultDB)[1][0]["activitypopis"];
                $notificationBody = "V tento deň spustila akciu: <strong>" . $activity . "</strong>.";
                break;
            case "obratybu":
                $activityid = $this->subjectDetails["logactivityid"];
                $destinacia = $this->destinacia;
                $pouzivatel = $this->getInitiatorName();
                $activity = Connection::getDataFromDatabase("SELECT activitypopis FROM activity WHERE activityid = $activityid AND destinacia = '$destinacia'", defaultDB)[1][0]["activitypopis"];
                $notificationBody = $pouzivatel["username"] . " vykonal akciu <strong>" . $activity . "</strong> Suma: <strong>" . number_format($this->subjectDetails["suma"], 2, ",", " ") . " " . $this->subjectDetails["mena"] . "</strong>";
                break;
            case "updateMenovyPar":
                $notificationBody = "Aktualizoval menový pár <strong>" . $this->subjectDetails["par"] . "</strong>";
                break;
            case "deleteMenovyPar":
                $notificationBody = "Odstránil menový pár <strong>" . $this->subjectDetails["par"] . "</strong>";
                break;
            case "createMenovyPar":
                $notificationBody = "Vytvoril menový pár <strong>" . $this->subjectDetails["par"] . "</strong>";
                break;
            default:
                echo $this->notificationCode;
        }
        return $notificationBody;
    }

    function getInitiatorName()
    {
        $userid = $this->subjectDetails["loguserid"];
        $initiatorName = Connection::getDataFromDatabase("SELECT username, userid FROM users WHERE userid = $userid", defaultDB)[1][0];
        return $initiatorName;
    }

    function render($notificationID, $isOld = false)
    {
        if ($isOld) {
            self::getNotificationsDetailsOLD($notificationID);
        } else {
            self::getNotificationsDetails($notificationID);
        }
        return self::createNotificationDynamicBody($notificationID);
    }
}